// slots service to fetch slots from the server or a mock data source
export interface Slots {
  id: number;
  date: string;
  startTime: string;
  endTime: string;
  price: number;
  isAvailable: boolean;
  isSelected: boolean;
}

const generateTimeSlots = () => {
  const slots: Slots[] = [];
  let id = 1;

  const startDate = new Date();
  const endDate = new Date("August 05, 2025");
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    const date = currentDate.toLocaleDateString("en-US", {
      month: "long",
      day: "2-digit",
      year: "numeric",
    });

    // Generate slots from 7:00 AM to 8:30 PM
    for (let hour = 7; hour <= 20; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        if (hour === 20 && minute === 30) break; // Stop at 8:30 PM

        const startHour = hour.toString().padStart(2, "0");
        const startMinute = minute.toString().padStart(2, "0");
        const endHour =
          minute === 30 ? (hour + 1).toString().padStart(2, "0") : startHour;
        const endMinute = minute === 30 ? "00" : "30";

        const amPmStart = hour >= 12 ? "pm" : "am";
        const amPmEnd =
          (hour >= 12 && minute === 30) || hour >= 13 ? "pm" : "am";
        const hour12Start = hour > 12 ? hour - 12 : hour;
        const hour12End =
          Number(endHour) > 12 ? Number(endHour) - 12 : Number(endHour);

        slots.push({
          id: id++,
          date: date,
          startTime: `${hour12Start}:${startMinute}${amPmStart}`,
          endTime: `${hour12End}:${endMinute}${amPmEnd}`,
          price: 15000,
          isAvailable: true,
          isSelected: false,
        });
      }
    }

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return slots;
};

const sampleSlots: Slots[] = generateTimeSlots();

export const slotsService = {
  getSlots(date: string) {
    // get all slots for a date
    return sampleSlots.filter((slot) => slot.date === date);
  },

  getSlot(slotId: number): Promise<Slots | undefined> {
    // get a single slot by id
    return Promise.resolve(sampleSlots.find((slot) => slot.id === slotId));
  },
};
