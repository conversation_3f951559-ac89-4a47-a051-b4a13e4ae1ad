-- FieldEase Database Schema - Row Level Security (RLS) Policies
-- Execute this file after creating tables and indexes

-- Enable RLS on all tables
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE slots ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin ENABLE ROW LEVEL SECURITY;

-- Bookings table policies
-- Allow public to insert bookings (for customer bookings)
CREATE POLICY "Allow public booking creation" ON bookings
    FOR INSERT TO public
    WITH CHECK (true);

-- Allow public to read their own bookings (if you implement user authentication later)
CREATE POLICY "Allow public booking read" ON bookings
    FOR SELECT TO public
    USING (true);

-- Allow authenticated users to read all bookings (for admin purposes)
CREATE POLICY "Allow authenticated booking read" ON bookings
    FOR SELECT TO authenticated
    USING (true);

-- Allow authenticated users to update bookings
CREATE POLICY "Allow authenticated booking update" ON bookings
    FOR UPDATE TO authenticated
    USING (true);

-- Allow authenticated users to delete bookings
CREATE POLICY "Allow authenticated booking delete" ON bookings
    FOR DELETE TO authenticated
    USING (true);

-- Slots table policies
-- Allow public to read available slots
CREATE POLICY "Allow public slot read" ON slots
    FOR SELECT TO public
    USING (true);

-- Allow authenticated users to manage slots
CREATE POLICY "Allow authenticated slot management" ON slots
    FOR ALL TO authenticated
    USING (true);

-- Allow public to update slot availability when booking
CREATE POLICY "Allow public slot booking" ON slots
    FOR UPDATE TO public
    USING (is_available = true)
    WITH CHECK (true);

-- Admin table policies
-- Only authenticated users can read admin data
CREATE POLICY "Allow authenticated admin read" ON admin
    FOR SELECT TO authenticated
    USING (true);

-- Only authenticated users can manage admin accounts
CREATE POLICY "Allow authenticated admin management" ON admin
    FOR ALL TO authenticated
    USING (true);

-- Additional security: Prevent public from directly modifying admin table
CREATE POLICY "Deny public admin access" ON admin
    FOR ALL TO public
    USING (false);

-- Comments for policy documentation
COMMENT ON POLICY "Allow public booking creation" ON bookings IS 'Customers can create bookings';
COMMENT ON POLICY "Allow public slot read" ON slots IS 'Anyone can view available slots';
COMMENT ON POLICY "Allow authenticated slot management" ON slots IS 'Authenticated users can manage all slots';
COMMENT ON POLICY "Allow public slot booking" ON slots IS 'Customers can book available slots';
