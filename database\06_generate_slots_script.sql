-- FieldEase Slot Generation Script
-- This script generates slots exactly like the frontend generateTimeSlots() function
-- Execute this in Supabase SQL Editor to populate slots table

-- Function to generate slots for a specific date range
CREATE OR REPLACE FUNCTION generate_fieldease_slots(
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE DEFAULT CURRENT_DATE + INTERVAL '365 days'
)
RETURNS INTEGER AS $$
DECLARE
    current_date DATE := start_date;
    slot_hour INTEGER;
    slot_minute INTEGER;
    start_time TIME;
    end_time TIME;
    slots_created INTEGER := 0;
BEGIN
    -- Clear existing slots in the date range (optional - uncomment if needed)
    -- DELETE FROM slots WHERE date BETWEEN start_date AND end_date;
    
    -- Loop through each date from start_date to end_date
    WHILE current_date <= end_date LOOP
        
        -- Generate slots from 7:00 AM to 8:30 PM (matches frontend logic)
        FOR slot_hour IN 7..20 LOOP
            FOR slot_minute IN 0..30 BY 30 LOOP
                
                -- Stop at 8:30 PM (matches frontend condition)
                IF slot_hour = 20 AND slot_minute = 30 THEN
                    EXIT;
                END IF;
                
                -- Calculate start time
                start_time := (slot_hour || ':' || LPAD(slot_minute::TEXT, 2, '0') || ':00')::TIME;
                
                -- Calculate end time (30 minutes later)
                IF slot_minute = 30 THEN
                    -- Next hour, minute 00
                    end_time := ((slot_hour + 1) || ':00:00')::TIME;
                ELSE
                    -- Same hour, minute 30
                    end_time := (slot_hour || ':30:00')::TIME;
                END IF;
                
                -- Insert the slot
                INSERT INTO slots (date, start_time, end_time, price, is_available)
                VALUES (current_date, start_time, end_time, 15000, TRUE)
                ON CONFLICT DO NOTHING; -- Prevent duplicates if running multiple times
                
                slots_created := slots_created + 1;
                
            END LOOP;
        END LOOP;
        
        -- Move to next day
        current_date := current_date + INTERVAL '1 day';
    END LOOP;
    
    RETURN slots_created;
END;
$$ LANGUAGE plpgsql;

-- Function to generate slots matching frontend date format and logic exactly
CREATE OR REPLACE FUNCTION generate_slots_frontend_compatible(
    days_ahead INTEGER DEFAULT 365
)
RETURNS TABLE (
    total_slots INTEGER,
    date_range TEXT
) AS $$
DECLARE
    start_date DATE := CURRENT_DATE;
    end_date DATE := CURRENT_DATE + (days_ahead || ' days')::INTERVAL;
    slots_count INTEGER;
BEGIN
    -- Call the main generation function
    SELECT generate_fieldease_slots(start_date, end_date) INTO slots_count;
    
    -- Return summary
    RETURN QUERY SELECT 
        slots_count,
        start_date::TEXT || ' to ' || end_date::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Quick execution scripts for different time periods

-- Generate slots for next 30 days
-- SELECT * FROM generate_slots_frontend_compatible(30);

-- Generate slots for next 90 days  
-- SELECT * FROM generate_slots_frontend_compatible(90);

-- Generate slots for next year (365 days)
-- SELECT * FROM generate_slots_frontend_compatible(365);

-- Generate slots for specific date range
-- SELECT generate_fieldease_slots('2024-08-01'::DATE, '2024-12-31'::DATE);

-- View generated slots for today
-- SELECT 
--     date,
--     start_time,
--     end_time,
--     price,
--     is_available
-- FROM slots 
-- WHERE date = CURRENT_DATE
-- ORDER BY start_time;

-- Count total slots generated
-- SELECT 
--     COUNT(*) as total_slots,
--     MIN(date) as first_date,
--     MAX(date) as last_date
-- FROM slots;

-- EXECUTE ONE OF THESE COMMANDS TO GENERATE SLOTS:

-- For next 60 days (recommended for testing):
SELECT * FROM generate_slots_frontend_compatible(60);

-- For next year:
-- SELECT * FROM generate_slots_frontend_compatible(365);
