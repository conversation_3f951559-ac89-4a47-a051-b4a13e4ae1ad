# FieldEase Database Schema

This directory contains SQL files to set up the complete database schema for the FieldEase field booking application in Supabase.

## Execution Order

Execute these files in the Supabase SQL Editor in the following order:

1. **01_create_tables.sql** - Creates the main database tables
2. **02_create_indexes.sql** - Creates performance indexes
3. **03_rls_policies.sql** - Sets up Row Level Security policies
4. **04_functions_triggers.sql** - Creates database functions and triggers
5. **05_sample_data.sql** - Populates tables with sample data (optional)

## Database Schema Overview

### Tables

#### bookings
- `id`: UUID (primary key, auto-generated)
- `name`: TEXT (required) - customer's full name
- `phone`: TEXT (required) - customer's contact phone number
- `email`: TEXT (required) - customer's email address
- `date`: DATE (required) - the date of the booking
- `created_at`: TIMESTAMPTZ (auto-generated with default NOW())

#### slots
- `id`: UUID (primary key, auto-generated)
- `date`: DATE (required) - the date when the slot is available
- `start_time`: TIME (required) - slot start time
- `end_time`: TIME (required) - slot end time
- `price`: INTEGER (required) - price in cents (e.g., 15000 for $150.00)
- `is_available`: BOOLEAN (required, default TRUE) - FALSE when slot is booked
- `booking_id`: UUID (nullable foreign key) - references bookings.id, NULL when unbooked
- `created_at`: TIMESTAMPTZ (auto-generated with default NOW())

#### admin
- `id`: UUID (primary key, auto-generated)
- `name`: TEXT (required) - administrator's full name
- `email`: TEXT (required, unique) - administrator's email address
- `created_at`: TIMESTAMPTZ (auto-generated with default NOW())

### Relationships

- **One-to-many**: One booking can have multiple slots, but each slot belongs to only one booking
- **Foreign key**: slots.booking_id references bookings.id with ON DELETE SET NULL

### Key Features

#### Constraints
- Email validation using regex patterns
- Positive price validation
- Time order validation (start_time < end_time)
- Non-empty name and phone validation

#### Indexes
- Performance indexes on frequently queried columns (date, start_time, is_available)
- Composite indexes for common query patterns
- Foreign key indexes for join performance

#### Row Level Security (RLS)
- Public access for viewing available slots and creating bookings
- Authenticated access for admin operations
- Secure policies preventing unauthorized data access

#### Functions and Triggers
- `prevent_double_booking()` - Prevents booking already booked slots
- `validate_slot_times()` - Ensures valid time ranges and durations
- `get_available_slots(date)` - Retrieves available slots for a specific date
- `book_slots()` - Atomic function to book multiple slots with a single booking

## Usage Examples

### Query available slots for a date
```sql
SELECT * FROM get_available_slots('2024-08-05');
```

### Book multiple slots
```sql
SELECT book_slots(
    'John Doe',
    '+************',
    '<EMAIL>',
    '2024-08-05',
    ARRAY['slot-uuid-1', 'slot-uuid-2']::UUID[]
);
```

### Get booking details with slots
```sql
SELECT 
    b.id as booking_id,
    b.name,
    b.email,
    b.date as booking_date,
    s.start_time,
    s.end_time,
    s.price
FROM bookings b
JOIN slots s ON s.booking_id = b.id
WHERE b.id = 'booking-uuid';
```

## TypeScript Integration

The schema is designed to work with your existing TypeScript interfaces:

```typescript
interface Slots {
  id: string; // UUID as string
  date: string;
  startTime: string; // Formatted from start_time
  endTime: string;   // Formatted from end_time
  price: number;
  isAvailable: boolean; // Maps to is_available
  isSelected: boolean;  // Client-side only
}
```

## Notes

- All prices are stored in cents to avoid floating-point precision issues
- UUIDs are used for all primary keys for better security and scalability
- The schema supports timezone-aware timestamps
- Sample data includes 60 days of slots with 30-minute intervals from 8 AM to 8 PM
- Sundays are excluded from sample data (assuming field closure)

## Security Considerations

- RLS policies ensure data isolation
- Email validation prevents invalid email addresses
- Constraints prevent data integrity issues
- Functions provide atomic operations for complex workflows
