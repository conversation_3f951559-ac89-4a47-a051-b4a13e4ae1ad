-- FieldEase Database Schema - Index Creation
-- Execute this file after creating tables

-- Indexes for bookings table
CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(date);
CREATE INDEX IF NOT EXISTS idx_bookings_email ON bookings(email);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON bookings(created_at);

-- Indexes for slots table
CREATE INDEX IF NOT EXISTS idx_slots_date ON slots(date);
CREATE INDEX IF NOT EXISTS idx_slots_start_time ON slots(start_time);
CREATE INDEX IF NOT EXISTS idx_slots_is_available ON slots(is_available);
CREATE INDEX IF NOT EXISTS idx_slots_booking_id ON slots(booking_id);
CREATE INDEX IF NOT EXISTS idx_slots_date_available ON slots(date, is_available);
CREATE INDEX IF NOT EXISTS idx_slots_date_start_time ON slots(date, start_time);

-- Composite index for common queries (available slots for a specific date)
CREATE INDEX IF NOT EXISTS idx_slots_date_available_start_time 
ON slots(date, is_available, start_time) 
WHERE is_available = TRUE;

-- Index for admin table
CREATE INDEX IF NOT EXISTS idx_admin_email ON admin(email);

-- Performance optimization indexes
CREATE INDEX IF NOT EXISTS idx_bookings_date_created_at ON bookings(date, created_at);
CREATE INDEX IF NOT EXISTS idx_slots_booking_date ON slots(booking_id, date) WHERE booking_id IS NOT NULL;
