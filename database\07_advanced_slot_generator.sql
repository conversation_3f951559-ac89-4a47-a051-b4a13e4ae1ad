-- Advanced FieldEase Slot Generation Script
-- This script provides more advanced slot generation with business rules

-- Function to clear existing slots (use with caution)
CREATE OR REPLACE FUNCTION clear_slots_in_range(
    start_date DATE,
    end_date DATE
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM slots 
    WHERE date BETWEEN start_date AND end_date
    AND booking_id IS NULL; -- Only delete unbooked slots
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to generate slots with business rules (skip weekends, holidays, etc.)
CREATE OR REPLACE FUNCTION generate_business_slots(
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE DEFAULT CURRENT_DATE + INTERVAL '90 days',
    skip_sundays BOOLEAN DEFAULT TRUE,
    skip_saturdays BOOLEAN DEFAULT FALSE,
    custom_price INTEGER DEFAULT 15000
)
RETURNS TABLE (
    slots_created INTEGER,
    days_processed INTEGER,
    days_skipped INTEGER
) AS $$
DECLARE
    current_date DATE := start_date;
    slot_hour INTEGER;
    slot_minute INTEGER;
    start_time TIME;
    end_time TIME;
    slots_count INTEGER := 0;
    days_count INTEGER := 0;
    skipped_count INTEGER := 0;
    day_of_week INTEGER;
BEGIN
    -- Loop through each date
    WHILE current_date <= end_date LOOP
        days_count := days_count + 1;
        day_of_week := EXTRACT(DOW FROM current_date); -- 0=Sunday, 6=Saturday
        
        -- Skip days based on business rules
        IF (skip_sundays AND day_of_week = 0) OR 
           (skip_saturdays AND day_of_week = 6) THEN
            skipped_count := skipped_count + 1;
            current_date := current_date + INTERVAL '1 day';
            CONTINUE;
        END IF;
        
        -- Generate slots for this day (7:00 AM to 8:30 PM)
        FOR slot_hour IN 7..20 LOOP
            FOR slot_minute IN 0..30 BY 30 LOOP
                
                -- Stop at 8:30 PM
                IF slot_hour = 20 AND slot_minute = 30 THEN
                    EXIT;
                END IF;
                
                -- Calculate times
                start_time := (slot_hour || ':' || LPAD(slot_minute::TEXT, 2, '0') || ':00')::TIME;
                
                IF slot_minute = 30 THEN
                    end_time := ((slot_hour + 1) || ':00:00')::TIME;
                ELSE
                    end_time := (slot_hour || ':30:00')::TIME;
                END IF;
                
                -- Insert slot (skip if already exists)
                INSERT INTO slots (date, start_time, end_time, price, is_available)
                VALUES (current_date, start_time, end_time, custom_price, TRUE)
                ON CONFLICT DO NOTHING;
                
                slots_count := slots_count + 1;
                
            END LOOP;
        END LOOP;
        
        current_date := current_date + INTERVAL '1 day';
    END LOOP;
    
    RETURN QUERY SELECT slots_count, days_count, skipped_count;
END;
$$ LANGUAGE plpgsql;

-- Function to regenerate slots (clear and recreate)
CREATE OR REPLACE FUNCTION regenerate_slots(
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE DEFAULT CURRENT_DATE + INTERVAL '60 days'
)
RETURNS TABLE (
    cleared_slots INTEGER,
    new_slots INTEGER,
    operation_summary TEXT
) AS $$
DECLARE
    cleared_count INTEGER;
    created_count INTEGER;
    days_processed INTEGER;
    days_skipped INTEGER;
BEGIN
    -- Clear existing unbooked slots
    SELECT clear_slots_in_range(start_date, end_date) INTO cleared_count;
    
    -- Generate new slots
    SELECT slots_created, days_processed, days_skipped 
    INTO created_count, days_processed, days_skipped
    FROM generate_business_slots(start_date, end_date, TRUE, FALSE, 15000);
    
    RETURN QUERY SELECT 
        cleared_count,
        created_count,
        'Processed ' || days_processed || ' days, skipped ' || days_skipped || ' days';
END;
$$ LANGUAGE plpgsql;

-- Function to get slot generation statistics
CREATE OR REPLACE FUNCTION get_slot_statistics()
RETURNS TABLE (
    total_slots BIGINT,
    available_slots BIGINT,
    booked_slots BIGINT,
    date_range_start DATE,
    date_range_end DATE,
    avg_slots_per_day NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_slots,
        COUNT(*) FILTER (WHERE is_available = TRUE) as available_slots,
        COUNT(*) FILTER (WHERE is_available = FALSE) as booked_slots,
        MIN(date) as date_range_start,
        MAX(date) as date_range_end,
        ROUND(COUNT(*)::NUMERIC / (MAX(date) - MIN(date) + 1), 2) as avg_slots_per_day
    FROM slots;
END;
$$ LANGUAGE plpgsql;

-- USAGE EXAMPLES:

-- 1. Generate slots for next 60 days (skip Sundays)
-- SELECT * FROM generate_business_slots(CURRENT_DATE, CURRENT_DATE + 60, TRUE, FALSE, 15000);

-- 2. Regenerate slots (clear and recreate)
-- SELECT * FROM regenerate_slots(CURRENT_DATE, CURRENT_DATE + 90);

-- 3. Generate slots with custom price for weekends
-- SELECT * FROM generate_business_slots(CURRENT_DATE, CURRENT_DATE + 30, FALSE, FALSE, 20000);

-- 4. Get statistics about generated slots
-- SELECT * FROM get_slot_statistics();

-- 5. Clear slots in a specific range (unbooked only)
-- SELECT clear_slots_in_range('2024-08-01'::DATE, '2024-08-31'::DATE);

-- RECOMMENDED: Generate slots for next 3 months
SELECT * FROM generate_business_slots(
    CURRENT_DATE, 
    CURRENT_DATE + INTERVAL '90 days', 
    TRUE,  -- Skip Sundays
    FALSE, -- Don't skip Saturdays  
    15000  -- Price in cents (150.00 TZS)
);
